%pip install pandas 

import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import seaborn as sns

## Lets create synthetic data points
from sklearn.datasets import make_classification

X,y=make_classification(n_samples=1000,n_features=2,n_classes=2,
                        n_clusters_per_class=2,n_redundant=0)

X

y

pd.DataFrame(X)[0]

sns.scatterplot(pd.DataFrame(X)[0],pd.DataFrame(X)[1],hue=y)

from sklearn.model_selection import train_test_split
X_train,X_test,y_train,y_test=train_test_split(X,y,test_size=0.25,random_state=10)

from sklearn.svm import SVC

svc=SVC(kernel='linear')

svc.fit(X_train,y_train)

svc.coef_

## Prediction
y_pred=svc.predict(X_test)

from sklearn.metrics import classification_report,confusion_matrix

print(classification_report(y_test,y_pred))
print(confusion_matrix(y_test,y_pred))

rbf=SVC(kernel='rbf')

rbf.fit(X_train,y_train)

## Prediction
y_pred1=rbf.predict(X_test)

print(classification_report(y_test,y_pred1))
print(confusion_matrix(y_test,y_pred1))

polynomial=SVC(kernel='poly')
polynomial.fit(X_train,y_train)
## Prediction
y_pred2=polynomial.predict(X_test)
print(classification_report(y_test,y_pred2))
print(confusion_matrix(y_test,y_pred2))

sigmoid=SVC(kernel='sigmoid')
sigmoid.fit(X_train,y_train)
## Prediction
y_pred3=sigmoid.predict(X_test)
print(classification_report(y_test,y_pred3))
print(confusion_matrix(y_test,y_pred3))

sigmoid.intercept_

from sklearn.model_selection import GridSearchCV
 
# defining parameter range
param_grid = {'C': [0.1, 1, 10, 100, 1000],
              'gamma': [1, 0.1, 0.01, 0.001, 0.0001],
              'kernel': ['rbf']}

grid=GridSearchCV(SVC(),param_grid=param_grid,refit=True,cv=5,verbose=3)

grid.fit(X_train,y_train)

grid.best_params_

## Prediction
y_pred4=grid.predict(X_test)
print(classification_report(y_test,y_pred4))
print(confusion_matrix(y_test,y_pred4))

