{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["## Support Vector Regression Implementation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["## Dataset (Tips Dataset)\n", "import seaborn as sns\n", "df=sns.load_dataset('tips')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>total_bill</th>\n", "      <th>tip</th>\n", "      <th>sex</th>\n", "      <th>smoker</th>\n", "      <th>day</th>\n", "      <th>time</th>\n", "      <th>size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>16.99</td>\n", "      <td>1.01</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10.34</td>\n", "      <td>1.66</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21.01</td>\n", "      <td>3.50</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>23.68</td>\n", "      <td>3.31</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>24.59</td>\n", "      <td>3.61</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   total_bill   tip     sex smoker  day    time  size\n", "0       16.99  1.01  Female     No  Sun  Dinner     2\n", "1       10.34  1.66    Male     No  Sun  Dinner     3\n", "2       21.01  3.50    Male     No  Sun  Dinner     3\n", "3       23.68  3.31    Male     No  Sun  Dinner     2\n", "4       24.59  3.61  Female     No  Sun  Dinner     4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 244 entries, 0 to 243\n", "Data columns (total 7 columns):\n", " #   Column      Non-Null Count  Dtype   \n", "---  ------      --------------  -----   \n", " 0   total_bill  244 non-null    float64 \n", " 1   tip         244 non-null    float64 \n", " 2   sex         244 non-null    category\n", " 3   smoker      244 non-null    category\n", " 4   day         244 non-null    category\n", " 5   time        244 non-null    category\n", " 6   size        244 non-null    int64   \n", "dtypes: category(4), float64(2), int64(1)\n", "memory usage: 7.3 KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["Male      157\n", "Female     87\n", "Name: sex, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df['sex'].value_counts()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["No     151\n", "Yes     93\n", "Name: smoker, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df['smoker'].value_counts()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Sat     87\n", "Sun     76\n", "<PERSON><PERSON>    62\n", "Fri     19\n", "Name: day, dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df['day'].value_counts()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["Dinner    176\n", "<PERSON><PERSON>      68\n", "Name: time, dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df['time'].value_counts()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['total_bill', 'tip', 'sex', 'smoker', 'day', 'time', 'size'], dtype='object')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["## independnent and dependent features\n", "X=df[['tip', 'sex', 'smoker', 'day', 'time', 'size']]\n", "y=df['total_bill']"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["##train test split\n", "from sklearn.model_selection import train_test_split\n", "X_train,X_test,y_train,y_test=train_test_split(X,y,test_size=0.25,random_state=10)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tip</th>\n", "      <th>sex</th>\n", "      <th>smoker</th>\n", "      <th>day</th>\n", "      <th>time</th>\n", "      <th>size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>1.76</td>\n", "      <td>Male</td>\n", "      <td>Yes</td>\n", "      <td>Sat</td>\n", "      <td>Dinner</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.66</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.50</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2.01</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Sat</td>\n", "      <td>Dinner</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>3.00</td>\n", "      <td>Male</td>\n", "      <td>Yes</td>\n", "      <td>Sun</td>\n", "      <td>Dinner</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tip   sex smoker  day    time  size\n", "58   1.76  Male    Yes  Sat  Dinner     2\n", "1    1.66  Male     No  Sun  Dinner     3\n", "2    3.50  Male     No  Sun  Dinner     3\n", "68   2.01  Male     No  Sat  Dinner     2\n", "184  3.00  Male    Yes  Sun  Dinner     2"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train.head()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["## Feature Encoding(LAbel Encoding And Onehot Encoding)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["le1=LabelEncoder()\n", "le2=LabelEncoder()\n", "le3=LabelEncoder()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "X_train['sex']=le1.fit_transform(X_train['sex'])\n", "X_train['smoker']=le2.fit_transform(X_train['smoker'])\n", "X_train['time']=le3.fit_transform(X_train['time'])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tip</th>\n", "      <th>sex</th>\n", "      <th>smoker</th>\n", "      <th>day</th>\n", "      <th>time</th>\n", "      <th>size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>1.76</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Sat</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.66</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Sun</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.50</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Sun</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2.01</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Sat</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>3.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Sun</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tip  sex  smoker  day  time  size\n", "58   1.76    1       1  Sat     0     2\n", "1    1.66    1       0  Sun     0     3\n", "2    3.50    1       0  Sun     0     3\n", "68   2.01    1       0  Sat     0     2\n", "184  3.00    1       1  Sun     0     2"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train.head()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["X_test['sex']=le1.transform(X_test['sex'])\n", "X_test['smoker']=le2.transform(X_test['smoker'])\n", "X_test['time']=le3.transform(X_test['time'])"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tip</th>\n", "      <th>sex</th>\n", "      <th>smoker</th>\n", "      <th>day</th>\n", "      <th>time</th>\n", "      <th>size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>2.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Sun</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>3.21</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Sat</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>2.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Sat</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>3.76</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Sat</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>2.09</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Sat</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tip  sex  smoker  day  time  size\n", "162  2.00    0       0  Sun     0     3\n", "60   3.21    1       1  Sat     0     2\n", "61   2.00    1       1  Sat     0     2\n", "63   3.76    1       1  Sat     0     4\n", "69   2.09    1       1  Sat     0     2"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test.head()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["## Onehot encoding--- ColumnTrnasformer\n", "\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["ct=ColumnTransformer(transformers=[('onehot',OneHotEncoder(drop='first'),[3])],\n", "                                   remainder='passthrough')"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy\n", "numpy.set_printoptions(threshold=sys.maxsize)\n", "X_train=ct.fit_transform(X_train)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["X_test=ct.transform(X_test)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.  , 1.  , 0.  , 2.  , 0.  , 0.  , 0.  , 3.  ],\n", "       [1.  , 0.  , 0.  , 3.21, 1.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 2.  , 1.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.76, 1.  , 1.  , 0.  , 4.  ],\n", "       [1.  , 0.  , 0.  , 2.09, 1.  , 1.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 5.  , 1.  , 1.  , 1.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 3.51, 1.  , 0.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 5.16, 1.  , 1.  , 0.  , 4.  ],\n", "       [0.  , 1.  , 0.  , 5.  , 1.  , 0.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.6 , 1.  , 0.  , 0.  , 3.  ],\n", "       [0.  , 1.  , 0.  , 5.65, 1.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 2.5 , 0.  , 1.  , 0.  , 3.  ],\n", "       [0.  , 0.  , 1.  , 1.44, 1.  , 0.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.09, 0.  , 1.  , 0.  , 4.  ],\n", "       [0.  , 1.  , 0.  , 2.  , 1.  , 0.  , 0.  , 4.  ],\n", "       [0.  , 0.  , 1.  , 1.36, 0.  , 0.  , 1.  , 3.  ],\n", "       [0.  , 0.  , 1.  , 2.  , 0.  , 0.  , 1.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 1.68, 0.  , 0.  , 1.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 3.5 , 0.  , 1.  , 0.  , 3.  ],\n", "       [1.  , 0.  , 0.  , 3.16, 1.  , 1.  , 0.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 1.71, 1.  , 0.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 2.5 , 0.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 2.  , 1.  , 0.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 1.63, 0.  , 0.  , 1.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 5.  , 1.  , 0.  , 0.  , 5.  ],\n", "       [0.  , 0.  , 1.  , 2.  , 0.  , 0.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.08, 1.  , 1.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 2.92, 0.  , 0.  , 1.  , 4.  ],\n", "       [0.  , 0.  , 1.  , 2.31, 1.  , 0.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 6.73, 1.  , 0.  , 0.  , 4.  ],\n", "       [0.  , 1.  , 0.  , 5.07, 1.  , 0.  , 0.  , 4.  ],\n", "       [1.  , 0.  , 0.  , 6.5 , 0.  , 1.  , 0.  , 3.  ],\n", "       [0.  , 1.  , 0.  , 2.  , 1.  , 0.  , 0.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 2.5 , 1.  , 0.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.18, 1.  , 0.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 1.1 , 0.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.35, 1.  , 0.  , 0.  , 3.  ],\n", "       [0.  , 0.  , 0.  , 2.2 , 1.  , 1.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 4.06, 1.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.  , 1.  , 1.  , 0.  , 4.  ],\n", "       [0.  , 0.  , 1.  , 3.  , 1.  , 1.  , 1.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 3.5 , 1.  , 1.  , 0.  , 3.  ],\n", "       [0.  , 0.  , 1.  , 1.5 , 0.  , 0.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.  , 1.  , 1.  , 0.  , 5.  ],\n", "       [0.  , 0.  , 1.  , 2.  , 0.  , 1.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 1.97, 1.  , 0.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 0.  , 3.5 , 1.  , 0.  , 0.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 2.  , 1.  , 0.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 1.73, 1.  , 0.  , 1.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 3.48, 0.  , 1.  , 0.  , 2.  ],\n", "       [1.  , 0.  , 0.  , 4.08, 1.  , 0.  , 0.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 6.  , 1.  , 0.  , 0.  , 4.  ],\n", "       [0.  , 0.  , 1.  , 3.23, 0.  , 1.  , 1.  , 3.  ],\n", "       [0.  , 1.  , 0.  , 4.71, 1.  , 0.  , 0.  , 4.  ],\n", "       [1.  , 0.  , 0.  , 1.  , 0.  , 0.  , 0.  , 1.  ],\n", "       [0.  , 0.  , 0.  , 3.  , 1.  , 1.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 2.01, 0.  , 1.  , 1.  , 2.  ],\n", "       [0.  , 0.  , 1.  , 2.  , 1.  , 0.  , 1.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 3.11, 1.  , 1.  , 0.  , 2.  ],\n", "       [0.  , 0.  , 0.  , 2.  , 0.  , 1.  , 1.  , 2.  ],\n", "       [0.  , 1.  , 0.  , 3.  , 1.  , 0.  , 0.  , 2.  ]])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["## SVR--Support Vector Regression\n", "from sklearn.svm import SVR\n", "svr=SVR()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["SVR()"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["svr.fit(X_train,y_train)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["y_pred=svr.predict(X_test)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.46028114561159283\n", "4.1486423210190235\n"]}], "source": ["from sklearn.metrics import r2_score,mean_absolute_error\n", "print(r2_score(y_test,y_pred))\n", "print(mean_absolute_error(y_test,y_pred))"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["## Hyperparameter Tuning using GridSearch CV"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import GridSearchCV\n", " \n", "# defining parameter range\n", "param_grid = {'C': [0.1, 1, 10, 100, 1000],\n", "              'gamma': [1, 0.1, 0.01, 0.001, 0.0001],\n", "              'kernel': ['rbf']}"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 5 folds for each of 25 candidates, totalling 125 fits\n", "[CV] C=0.1, gamma=1, kernel=rbf ......................................\n", "[CV] ......... C=0.1, gamma=1, kernel=rbf, score=-0.067, total=   0.0s\n", "[CV] C=0.1, gamma=1, kernel=rbf ......................................\n", "[CV] ......... C=0.1, gamma=1, kernel=rbf, score=-0.058, total=   0.0s\n", "[CV] C=0.1, gamma=1, kernel=rbf ......................................\n", "[CV] ......... C=0.1, gamma=1, kernel=rbf, score=-0.145, total=   0.0s\n", "[CV] C=0.1, gamma=1, kernel=rbf ......................................\n", "[CV] .......... C=0.1, gamma=1, kernel=rbf, score=0.025, total=   0.0s\n", "[CV] C=0.1, gamma=1, kernel=rbf ......................................\n", "[CV] ......... C=0.1, gamma=1, kernel=rbf, score=-0.089, total=   0.0s\n", "[CV] C=0.1, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=0.1, gamma=0.1, kernel=rbf, score=0.013, total=   0.0s\n", "[CV] C=0.1, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=0.1, gamma=0.1, kernel=rbf, score=0.021, total=   0.0s\n", "[CV] C=0.1, gamma=0.1, kernel=rbf ....................................\n", "[CV] ....... C=0.1, gamma=0.1, kernel=rbf, score=-0.010, total=   0.0s\n", "[CV] C=0.1, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=0.1, gamma=0.1, kernel=rbf, score=0.124, total=   0.0s\n", "[CV] C=0.1, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=0.1, gamma=0.1, kernel=rbf, score=0.050, total=   0.0s\n", "[CV] C=0.1, gamma=0.01, kernel=rbf ...................................\n", "[CV] ...... C=0.1, gamma=0.01, kernel=rbf, score=-0.053, total=   0.0s\n", "[CV] C=0.1, gamma=0.01, kernel=rbf ...................................\n", "[CV] ...... C=0.1, gamma=0.01, kernel=rbf, score=-0.028, total=   0.0s\n", "[CV] C=0.1, gamma=0.01, kernel=rbf ...................................\n", "[CV] ...... C=0.1, gamma=0.01, kernel=rbf, score=-0.108, total=   0.0s\n", "[CV] C=0.1, gamma=0.01, kernel=rbf ...................................\n", "[CV] ....... C=0.1, gamma=0.01, kernel=rbf, score=0.040, total=   0.0s\n", "[CV] C=0.1, gamma=0.01, kernel=rbf ...................................\n", "[CV] ...... C=0.1, gamma=0.01, kernel=rbf, score=-0.058, total=   0.0s\n", "[CV] C=0.1, gamma=0.001, kernel=rbf ..................................\n", "[CV] ..... C=0.1, gamma=0.001, kernel=rbf, score=-0.080, total=   0.0s\n", "[CV] C=0.1, gamma=0.001, kernel=rbf ..................................\n", "[CV] ..... C=0.1, gamma=0.001, kernel=rbf, score=-0.068, total=   0.0s\n", "[CV] C=0.1, gamma=0.001, kernel=rbf ..................................\n", "[CV] ..... C=0.1, gamma=0.001, kernel=rbf, score=-0.167, total=   0.0s\n", "[CV] C=0.1, gamma=0.001, kernel=rbf ..................................\n", "[CV] ..... C=0.1, gamma=0.001, kernel=rbf, score=-0.006, total=   0.0s\n", "[CV] C=0.1, gamma=0.001, kernel=rbf ..................................\n", "[CV] ..... C=0.1, gamma=0.001, kernel=rbf, score=-0.105, total=   0.0s\n", "[CV] C=0.1, gamma=0.0001, kernel=rbf .................................\n", "[CV] .... C=0.1, gamma=0.0001, kernel=rbf, score=-0.083, total=   0.0s\n", "[CV] C=0.1, gamma=0.0001, kernel=rbf .................................\n", "[CV] .... C=0.1, gamma=0.0001, kernel=rbf, score=-0.073, total=   0.0s\n", "[CV] C=0.1, gamma=0.0001, kernel=rbf .................................\n", "[CV] .... C=0.1, gamma=0.0001, kernel=rbf, score=-0.173, total=   0.0s\n", "[CV] C=0.1, gamma=0.0001, kernel=rbf .................................\n", "[CV] .... C=0.1, gamma=0.0001, kernel=rbf, score=-0.013, total=   0.0s\n", "[CV] C=0.1, gamma=0.0001, kernel=rbf .................................\n", "[CV] .... C=0.1, gamma=0.0001, kernel=rbf, score=-0.110, total=   0.0s\n", "[CV] C=1, gamma=1, kernel=rbf ........................................\n", "[CV] ............ C=1, gamma=1, kernel=rbf, score=0.018, total=   0.0s\n", "[CV] C=1, gamma=1, kernel=rbf ........................................\n", "[CV] ............ C=1, gamma=1, kernel=rbf, score=0.051, total=   0.0s\n", "[CV] C=1, gamma=1, kernel=rbf ........................................\n", "[CV] ............ C=1, gamma=1, kernel=rbf, score=0.080, total=   0.0s\n", "[CV] C=1, gamma=1, kernel=rbf ........................................\n", "[CV] ............ C=1, gamma=1, kernel=rbf, score=0.166, total=   0.0s\n", "[CV] C=1, gamma=1, kernel=rbf ........................................\n", "[CV] ............ C=1, gamma=1, kernel=rbf, score=0.081, total=   0.0s\n", "[CV] C=1, gamma=0.1, kernel=rbf ......................................\n", "[CV] .......... C=1, gamma=0.1, kernel=rbf, score=0.189, total=   0.0s\n", "[CV] C=1, gamma=0.1, kernel=rbf ......................................\n", "[CV] .......... C=1, gamma=0.1, kernel=rbf, score=0.254, total=   0.0s\n", "[CV] C=1, gamma=0.1, kernel=rbf ......................................\n", "[CV] .......... C=1, gamma=0.1, kernel=rbf, score=0.533, total=   0.0s\n", "[CV] C=1, gamma=0.1, kernel=rbf ......................................\n", "[CV] .......... C=1, gamma=0.1, kernel=rbf, score=0.347, total=   0.0s\n", "[CV] C=1, gamma=0.1, kernel=rbf ......................................\n", "[CV] .......... C=1, gamma=0.1, kernel=rbf, score=0.503, total=   0.0s\n", "[CV] C=1, gamma=0.01, kernel=rbf .....................................\n", "[CV] ......... C=1, gamma=0.01, kernel=rbf, score=0.121, total=   0.0s\n", "[CV] C=1, gamma=0.01, kernel=rbf .....................................\n", "[CV] ......... C=1, gamma=0.01, kernel=rbf, score=0.221, total=   0.0s\n", "[CV] C=1, gamma=0.01, kernel=rbf .....................................\n", "[CV] ......... C=1, gamma=0.01, kernel=rbf, score=0.269, total=   0.0s\n", "[CV] C=1, gamma=0.01, kernel=rbf .....................................\n", "[CV] ......... C=1, gamma=0.01, kernel=rbf, score=0.305, total=   0.0s\n", "[CV] C=1, gamma=0.01, kernel=rbf .....................................\n", "[CV] ......... C=1, gamma=0.01, kernel=rbf, score=0.252, total=   0.0s\n", "[CV] C=1, gamma=0.001, kernel=rbf ....................................\n", "[CV] ....... C=1, gamma=0.001, kernel=rbf, score=-0.049, total=   0.0s\n", "[CV] C=1, gamma=0.001, kernel=rbf ....................................\n", "[CV] ....... C=1, gamma=0.001, kernel=rbf, score=-0.014, total=   0.0s\n", "[CV] C=1, gamma=0.001, kernel=rbf ....................................\n", "[CV] ....... C=1, gamma=0.001, kernel=rbf, score=-0.096, total=   0.0s\n", "[CV] C=1, gamma=0.001, kernel=rbf ....................................\n", "[CV] ........ C=1, gamma=0.001, kernel=rbf, score=0.050, total=   0.0s\n", "[CV] C=1, gamma=0.001, kernel=rbf ....................................\n", "[CV] ....... C=1, gamma=0.001, kernel=rbf, score=-0.051, total=   0.0s\n", "[CV] C=1, gamma=0.0001, kernel=rbf ...................................\n", "[CV] ...... C=1, gamma=0.0001, kernel=rbf, score=-0.080, total=   0.0s\n", "[CV] C=1, gamma=0.0001, kernel=rbf ...................................\n", "[CV] ...... C=1, gamma=0.0001, kernel=rbf, score=-0.068, total=   0.0s\n", "[CV] C=1, gamma=0.0001, kernel=rbf ...................................\n", "[CV] ...... C=1, gamma=0.0001, kernel=rbf, score=-0.167, total=   0.0s\n", "[CV] C=1, gamma=0.0001, kernel=rbf ...................................\n", "[CV] ...... C=1, gamma=0.0001, kernel=rbf, score=-0.006, total=   0.0s\n", "[CV] C=1, gamma=0.0001, kernel=rbf ...................................\n", "[CV] ...... C=1, gamma=0.0001, kernel=rbf, score=-0.104, total=   0.0s\n", "[CV] C=10, gamma=1, kernel=rbf .......................................\n", "[CV] ........... C=10, gamma=1, kernel=rbf, score=0.166, total=   0.0s\n", "[CV] C=10, gamma=1, kernel=rbf .......................................\n", "[CV] ........... C=10, gamma=1, kernel=rbf, score=0.231, total=   0.0s\n", "[CV] C=10, gamma=1, kernel=rbf .......................................\n", "[CV] ........... C=10, gamma=1, kernel=rbf, score=0.468, total=   0.0s\n", "[CV] C=10, gamma=1, kernel=rbf .......................................\n", "[CV] ........... C=10, gamma=1, kernel=rbf, score=0.212, total=   0.0s\n", "[CV] C=10, gamma=1, kernel=rbf .......................................\n", "[CV] ........... C=10, gamma=1, kernel=rbf, score=0.377, total=   0.0s\n", "[CV] C=10, gamma=0.1, kernel=rbf .....................................\n", "[CV] ......... C=10, gamma=0.1, kernel=rbf, score=0.156, total=   0.0s\n", "[CV] C=10, gamma=0.1, kernel=rbf .....................................\n", "[CV] ......... C=10, gamma=0.1, kernel=rbf, score=0.433, total=   0.0s\n", "[CV] C=10, gamma=0.1, kernel=rbf .....................................\n", "[CV] ......... C=10, gamma=0.1, kernel=rbf, score=0.706, total=   0.0s\n", "[CV] C=10, gamma=0.1, kernel=rbf .....................................\n", "[CV] ......... C=10, gamma=0.1, kernel=rbf, score=0.395, total=   0.0s\n", "[CV] C=10, gamma=0.1, kernel=rbf .....................................\n", "[CV] ......... C=10, gamma=0.1, kernel=rbf, score=0.541, total=   0.0s\n", "[CV] C=10, gamma=0.01, kernel=rbf ....................................\n", "[CV] ........ C=10, gamma=0.01, kernel=rbf, score=0.204, total=   0.0s\n", "[CV] C=10, gamma=0.01, kernel=rbf ....................................\n", "[CV] ........ C=10, gamma=0.01, kernel=rbf, score=0.474, total=   0.0s\n", "[CV] C=10, gamma=0.01, kernel=rbf ....................................\n", "[CV] ........ C=10, gamma=0.01, kernel=rbf, score=0.701, total=   0.0s\n", "[CV] C=10, gamma=0.01, kernel=rbf ....................................\n", "[CV] ........ C=10, gamma=0.01, kernel=rbf, score=0.432, total=   0.0s\n", "[CV] C=10, gamma=0.01, kernel=rbf ....................................\n", "[CV] ........ C=10, gamma=0.01, kernel=rbf, score=0.592, total=   0.0s\n", "[CV] C=10, gamma=0.001, kernel=rbf ...................................\n", "[CV] ....... C=10, gamma=0.001, kernel=rbf, score=0.144, total=   0.0s\n", "[CV] C=10, gamma=0.001, kernel=rbf ...................................\n", "[CV] ....... C=10, gamma=0.001, kernel=rbf, score=0.277, total=   0.0s\n", "[CV] C=10, gamma=0.001, kernel=rbf ...................................\n", "[CV] ....... C=10, gamma=0.001, kernel=rbf, score=0.338, total=   0.0s\n", "[CV] C=10, gamma=0.001, kernel=rbf ...................................\n", "[CV] ....... C=10, gamma=0.001, kernel=rbf, score=0.342, total=   0.0s\n", "[CV] C=10, gamma=0.001, kernel=rbf ...................................\n", "[CV] ....... C=10, gamma=0.001, kernel=rbf, score=0.283, total=   0.0s\n", "[CV] C=10, gamma=0.0001, kernel=rbf ..................................\n", "[CV] ..... C=10, gamma=0.0001, kernel=rbf, score=-0.049, total=   0.0s\n", "[CV] C=10, gamma=0.0001, kernel=rbf ..................................\n", "[CV] ..... C=10, gamma=0.0001, kernel=rbf, score=-0.013, total=   0.0s\n", "[CV] C=10, gamma=0.0001, kernel=rbf ..................................\n", "[CV] ..... C=10, gamma=0.0001, kernel=rbf, score=-0.094, total=   0.0s\n", "[CV] C=10, gamma=0.0001, kernel=rbf ..................................\n", "[CV] ...... C=10, gamma=0.0001, kernel=rbf, score=0.051, total=   0.0s\n", "[CV] C=10, gamma=0.0001, kernel=rbf ..................................\n", "[CV] ..... C=10, gamma=0.0001, kernel=rbf, score=-0.050, total=   0.0s\n", "[CV] C=100, gamma=1, kernel=rbf ......................................\n", "[CV] ......... C=100, gamma=1, kernel=rbf, score=-0.040, total=   0.0s\n", "[CV] C=100, gamma=1, kernel=rbf ......................................\n", "[CV] .......... C=100, gamma=1, kernel=rbf, score=0.190, total=   0.0s\n", "[CV] C=100, gamma=1, kernel=rbf ......................................\n", "[CV] .......... C=100, gamma=1, kernel=rbf, score=0.429, total=   0.0s\n", "[CV] C=100, gamma=1, kernel=rbf ......................................\n", "[CV] ......... C=100, gamma=1, kernel=rbf, score=-0.095, total=   0.0s\n", "[CV] C=100, gamma=1, kernel=rbf ......................................\n", "[CV] .......... C=100, gamma=1, kernel=rbf, score=0.242, total=   0.0s\n", "[CV] C=100, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=100, gamma=0.1, kernel=rbf, score=0.147, total=   0.0s\n", "[CV] C=100, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=100, gamma=0.1, kernel=rbf, score=0.537, total=   0.0s\n", "[CV] C=100, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=100, gamma=0.1, kernel=rbf, score=0.710, total=   0.0s\n", "[CV] C=100, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=100, gamma=0.1, kernel=rbf, score=0.499, total=   0.0s\n", "[CV] C=100, gamma=0.1, kernel=rbf ....................................\n", "[CV] ........ C=100, gamma=0.1, kernel=rbf, score=0.354, total=   0.0s\n", "[CV] C=100, gamma=0.01, kernel=rbf ...................................\n", "[CV] ....... C=100, gamma=0.01, kernel=rbf, score=0.154, total=   0.0s\n", "[CV] C=100, gamma=0.01, kernel=rbf ...................................\n", "[CV] ....... C=100, gamma=0.01, kernel=rbf, score=0.515, total=   0.0s\n", "[CV] C=100, gamma=0.01, kernel=rbf ...................................\n", "[CV] ....... C=100, gamma=0.01, kernel=rbf, score=0.727, total=   0.0s\n", "[CV] C=100, gamma=0.01, kernel=rbf ...................................\n", "[CV] ....... C=100, gamma=0.01, kernel=rbf, score=0.368, total=   0.0s\n", "[CV] C=100, gamma=0.01, kernel=rbf ...................................\n", "[CV] ....... C=100, gamma=0.01, kernel=rbf, score=0.581, total=   0.0s\n", "[CV] C=100, gamma=0.001, kernel=rbf ..................................\n", "[CV] ...... C=100, gamma=0.001, kernel=rbf, score=0.215, total=   0.0s\n", "[CV] C=100, gamma=0.001, kernel=rbf ..................................\n", "[CV] ...... C=100, gamma=0.001, kernel=rbf, score=0.502, total=   0.0s\n", "[CV] C=100, gamma=0.001, kernel=rbf ..................................\n", "[CV] ...... C=100, gamma=0.001, kernel=rbf, score=0.717, total=   0.0s\n", "[CV] C=100, gamma=0.001, kernel=rbf ..................................\n", "[CV] ...... C=100, gamma=0.001, kernel=rbf, score=0.420, total=   0.0s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.0s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   2 out of   2 | elapsed:    0.0s remaining:    0.0s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[CV] C=100, gamma=0.001, kernel=rbf ..................................\n", "[CV] ...... C=100, gamma=0.001, kernel=rbf, score=0.601, total=   0.0s\n", "[CV] C=100, gamma=0.0001, kernel=rbf .................................\n", "[CV] ..... C=100, gamma=0.0001, kernel=rbf, score=0.146, total=   0.0s\n", "[CV] C=100, gamma=0.0001, kernel=rbf .................................\n", "[CV] ..... C=100, gamma=0.0001, kernel=rbf, score=0.284, total=   0.0s\n", "[CV] C=100, gamma=0.0001, kernel=rbf .................................\n", "[CV] ..... C=100, gamma=0.0001, kernel=rbf, score=0.346, total=   0.0s\n", "[CV] C=100, gamma=0.0001, kernel=rbf .................................\n", "[CV] ..... C=100, gamma=0.0001, kernel=rbf, score=0.346, total=   0.0s\n", "[CV] C=100, gamma=0.0001, kernel=rbf .................................\n", "[CV] ..... C=100, gamma=0.0001, kernel=rbf, score=0.288, total=   0.0s\n", "[CV] C=1000, gamma=1, kernel=rbf .....................................\n", "[CV] ........ C=1000, gamma=1, kernel=rbf, score=-0.908, total=   0.0s\n", "[CV] C=1000, gamma=1, kernel=rbf .....................................\n", "[CV] ......... C=1000, gamma=1, kernel=rbf, score=0.139, total=   0.0s\n", "[CV] C=1000, gamma=1, kernel=rbf .....................................\n", "[CV] ......... C=1000, gamma=1, kernel=rbf, score=0.040, total=   0.0s\n", "[CV] C=1000, gamma=1, kernel=rbf .....................................\n", "[CV] ........ C=1000, gamma=1, kernel=rbf, score=-1.755, total=   0.0s\n", "[CV] C=1000, gamma=1, kernel=rbf .....................................\n", "[CV] ......... C=1000, gamma=1, kernel=rbf, score=0.056, total=   0.0s\n", "[CV] C=1000, gamma=0.1, kernel=rbf ...................................\n", "[CV] ....... C=1000, gamma=0.1, kernel=rbf, score=0.042, total=   0.0s\n", "[CV] C=1000, gamma=0.1, kernel=rbf ...................................\n", "[CV] ....... C=1000, gamma=0.1, kernel=rbf, score=0.493, total=   0.0s\n", "[CV] C=1000, gamma=0.1, kernel=rbf ...................................\n", "[CV] ....... C=1000, gamma=0.1, kernel=rbf, score=0.114, total=   0.0s\n", "[CV] C=1000, gamma=0.1, kernel=rbf ...................................\n", "[CV] ....... C=1000, gamma=0.1, kernel=rbf, score=0.048, total=   0.0s\n", "[CV] C=1000, gamma=0.1, kernel=rbf ...................................\n", "[CV] ....... C=1000, gamma=0.1, kernel=rbf, score=0.284, total=   0.0s\n", "[CV] C=1000, gamma=0.01, kernel=rbf ..................................\n", "[CV] ...... C=1000, gamma=0.01, kernel=rbf, score=0.120, total=   0.0s\n", "[CV] C=1000, gamma=0.01, kernel=rbf ..................................\n", "[CV] ...... C=1000, gamma=0.01, kernel=rbf, score=0.572, total=   0.0s\n", "[CV] C=1000, gamma=0.01, kernel=rbf ..................................\n", "[CV] ...... C=1000, gamma=0.01, kernel=rbf, score=0.752, total=   0.0s\n", "[CV] C=1000, gamma=0.01, kernel=rbf ..................................\n", "[CV] ...... C=1000, gamma=0.01, kernel=rbf, score=0.425, total=   0.0s\n", "[CV] C=1000, gamma=0.01, kernel=rbf ..................................\n", "[CV] ...... C=1000, gamma=0.01, kernel=rbf, score=0.517, total=   0.0s\n", "[CV] C=1000, gamma=0.001, kernel=rbf .................................\n", "[CV] ..... C=1000, gamma=0.001, kernel=rbf, score=0.217, total=   0.0s\n", "[CV] C=1000, gamma=0.001, kernel=rbf .................................\n", "[CV] ..... C=1000, gamma=0.001, kernel=rbf, score=0.518, total=   0.0s\n", "[CV] C=1000, gamma=0.001, kernel=rbf .................................\n", "[CV] ..... C=1000, gamma=0.001, kernel=rbf, score=0.716, total=   0.0s\n", "[CV] C=1000, gamma=0.001, kernel=rbf .................................\n", "[CV] ..... C=1000, gamma=0.001, kernel=rbf, score=0.347, total=   0.0s\n", "[CV] C=1000, gamma=0.001, kernel=rbf .................................\n", "[CV] ..... C=1000, gamma=0.001, kernel=rbf, score=0.635, total=   0.0s\n", "[CV] C=1000, gamma=0.0001, kernel=rbf ................................\n", "[CV] .... C=1000, gamma=0.0001, kernel=rbf, score=0.216, total=   0.0s\n", "[CV] C=1000, gamma=0.0001, kernel=rbf ................................\n", "[CV] .... C=1000, gamma=0.0001, kernel=rbf, score=0.505, total=   0.0s\n", "[CV] C=1000, gamma=0.0001, kernel=rbf ................................\n", "[CV] .... C=1000, gamma=0.0001, kernel=rbf, score=0.718, total=   0.0s\n", "[CV] C=1000, gamma=0.0001, kernel=rbf ................................\n", "[CV] .... C=1000, gamma=0.0001, kernel=rbf, score=0.420, total=   0.0s\n", "[CV] C=1000, gamma=0.0001, kernel=rbf ................................\n", "[CV] .... C=1000, gamma=0.0001, kernel=rbf, score=0.602, total=   0.0s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=1)]: Done 125 out of 125 | elapsed:    0.2s finished\n"]}, {"data": {"text/plain": ["GridSearchCV(estimator=SVR(),\n", "             param_grid={'C': [0.1, 1, 10, 100, 1000],\n", "                         'gamma': [1, 0.1, 0.01, 0.001, 0.0001],\n", "                         'kernel': ['rbf']},\n", "             verbose=3)"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["grid = GridSearchCV(SVR(), param_grid, refit = True, verbose = 3)\n", " \n", "# fitting the model for grid search\n", "grid.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'C': 1000, 'gamma': 0.0001, 'kernel': 'rbf'}"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["grid.best_params_"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["grid_prediction=grid.predict(X_test)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.5081618245078687\n", "3.8685147526100234\n"]}], "source": ["from sklearn.metrics import r2_score,mean_absolute_error\n", "print(r2_score(y_test,grid_prediction))\n", "print(mean_absolute_error(y_test,grid_prediction))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}